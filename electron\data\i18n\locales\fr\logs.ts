/**
 * Messages de journal - Français
 */
export default {
  reportController: {
    getCommonReportListEntry: "getCommonReportList paramètres d'entrée",
    getCommonReportListReturn: "getCommonReportList retour",
    getCommonReportListError: "getCommonReportList exception",
    cancelUploadStart: "Annuler le téléchargement du fichier d'onde commencé",
    cancelUploadError: "Annuler le téléchargement du fichier d'onde exception",
    openWaveFileStart: "Ouvrir le fichier d'onde commencé",
    openWaveFileError: "Ouvrir le fichier d'onde exception",
    getGroupReportStart: "Obtenir le rapport de groupe commencé",
    getGroupReportError: "Obtenir le rapport de groupe exception",
    getOperateReportStart: "Obtenir le rapport d'opération commencé",
    getOperateReportError: "Obtenir le rapport d'opération exception",
    getAuditReportStart: "Obtenir le rapport d'audit commencé",
    getAuditReportError: "Obtenir le rapport d'audit exception",
    exportCommonReportStart: "Exporter le rapport commun commencé",
    exportCommonReportError: "Exporter le rapport commun exception",
    clearReportStart: "Effacer le rapport commencé",
    clearReportError: "Effacer le rapport exception",
    refreshReportStart: "Actualiser le rapport commun commencé",
    refreshReportError: "Actualiser le rapport commun exception",
    refreshGroupReportStart: "Actualiser le rapport de groupe commencé",
    refreshGroupReportError: "Actualiser le rapport de groupe exception",
    refreshOperateReportStart: "Actualiser le rapport d'opération commencé",
    refreshOperateReportError: "Actualiser le rapport d'opération exception",
    refreshTripReportStart: "Actualiser le rapport de déclenchement commencé",
    refreshTripReportError: "Actualiser le rapport de déclenchement exception",
    uploadWaveStart: "Téléchargement du fichier d'onde commencé",
    uploadWaveError: "Téléchargement du fichier d'onde exception"
  },
  configureService: {
    getConfigureListError: "Erreur lors de l'obtention de la liste de configuration",
    loadConfigureError: "Erreur lors du chargement de la configuration"
  },
  // Journaux du contrôleur
  configureController: {
    initialized: "Contrôleur initialisé",
    getConfigureListStart: "Commencer à obtenir la liste de configuration",
    getConfigureListError: "Erreur lors de l'obtention de la liste de configuration",
    addConfigureStart: "Commencer à ajouter la configuration",
    addConfigureError: "Erreur lors de l'ajout de la configuration",
    renameConfigureStart: "Commencer à renommer la configuration",
    renameConfigureError: "Erreur lors du renommage de la configuration",
    removeConfigureStart: "Commencer à supprimer la configuration",
    removeConfigureError: "Erreur lors de la suppression de la configuration",
    saveConfigureStart: "Commencer à sauvegarder la configuration",
    saveConfigureError: "Erreur lors de la sauvegarde de la configuration",
    loadConfigureStart: "Commencer à charger la configuration",
    loadConfigureError: "Erreur lors du chargement de la configuration",
    openConfigureDirStart: "Commencer à ouvrir le répertoire de configuration",
    openConfigureDirError: "Erreur lors de l'ouverture du répertoire de configuration"
  },
  deviceConnectController: {
    initialized: "Contrôleur initialisé",
    connectDeviceStart: "Commencer la connexion du dispositif, paramètres de connexion",
    connectDeviceCallService: "Appeler la couche de service pour connecter le dispositif",
    connectDeviceServiceResult: "Résultat de retour de la couche de service",
    connectDeviceSuccess: "Connexion du dispositif réussie",
    connectDeviceGetError: "Obtenir les informations d'erreur, sortie de journal",
    connectDeviceFailed: "Connexion du dispositif échouée, informations d'erreur",
    connectDeviceException: "Capturer l'exception, sortie de journal",
    connectDeviceExceptionDetail: "Exception de connexion du dispositif",
    disconnectDeviceStart: "Commencer la déconnexion du dispositif, ID du dispositif",
    disconnectDeviceCheckStatus: "Vérifier l'état de connexion du dispositif",
    disconnectDeviceAlready: "Dispositif déjà déconnecté, ID du dispositif",
    disconnectDeviceCallService: "Appeler la couche de service pour déconnecter le dispositif",
    disconnectDeviceResult: "Résultat de déconnexion",
    disconnectDeviceSuccess: "Déconnexion du dispositif réussie, ID du dispositif",
    disconnectDeviceException: "Exception de déconnexion du dispositif, ID du dispositif",
    disconnectDeviceFailed: "Déconnexion du dispositif échouée, ID du dispositif"
  },
  deviceOperateController: {
    initialized: "Contrôleur initialisé",
    addDeviceStart: "Commencer à ajouter la configuration du dispositif, paramètres de demande",
    addDeviceCallService: "Appeler la couche de service pour ajouter la configuration du dispositif",
    addDeviceSuccess: "Ajouter la configuration du dispositif réussi, résultat",
    addDeviceException: "Exception lors de l'ajout de la configuration du dispositif",
    updateDeviceStart: "Commencer à mettre à jour la configuration du dispositif, paramètres de demande",
    updateDeviceCallService: "Appeler la couche de service pour mettre à jour la configuration du dispositif",
    updateDeviceSuccess: "Mettre à jour la configuration du dispositif réussi, résultat",
    updateDeviceException: "Exception lors de la mise à jour de la configuration du dispositif",
    removeDeviceStart: "Commencer à supprimer la configuration du dispositif, paramètres de demande",
    removeDeviceCallService: "Appeler la couche de service pour supprimer la configuration du dispositif, ID du dispositif",
    removeDeviceResult: "Résultat de suppression de la configuration du dispositif",
    removeDeviceSuccess: "Supprimer la configuration du dispositif réussi, ID du dispositif",
    removeDeviceFailed: "Supprimer la configuration du dispositif échoué, ID du dispositif",
    removeDeviceException: "Exception lors de la suppression de la configuration du dispositif, ID du dispositif",
    getDeviceListStart: "Commencer à obtenir la liste de configuration du dispositif",
    getDeviceListCallService: "Appeler la couche de service pour obtenir la liste de configuration du dispositif",
    getDeviceListSuccess: "Obtenir la liste de configuration du dispositif réussi, nombre de dispositifs",
    getDeviceListException: "Exception lors de l'obtention de la liste de configuration du dispositif"
  },
  paramService: {
    getDiffParamComplete: "Comparaison terminée, groupes de différence",
    getAllDiffParamError: "getAllDiffParam erreur",
    getParamInfoEntry: "getParamInfo paramètres d'entrée",
    getParamInfoReturn: "getParamInfo retour",
    getParamInfoError: "getParamInfo exception",
    startGetParamInfo: "Commencer à obtenir les paramètres, pagination",
    getAllParamInfoStart: "Commencer à obtenir tous les paramètres",
    getAllParamInfoSuccess: "Obtenu avec succès tous les paramètres, total",
    modifyParamStart: "Commencer à modifier les paramètres",
    validateParam: "Valider l'élément de paramètre",
    validateFailed: "Validation du paramètre échouée",
    validatePassed: "Validation du paramètre réussie, prêt à envoyer",
    setTimeout: "Définir le délai d'attente",
    sendResponse: "Envoyer la réponse",
    modifySuccess: "Modification réussie",
    sendFailed: "Envoi échoué",
    businessError: "Erreur métier",
    getAllDiffParamStart: "Commencer la comparaison de paramètres par lots",
    excelParseFailed: "Analyse Excel échouée",
    csvParseFailed: "Analyse CSV échouée",
    xmlParseFailed: "Analyse XML échouée",
    fileParseComplete: "Analyse de fichier terminée",
    getDiffParamStart: "Commencer la comparaison de paramètres de groupe unique",
    diffComplete: "Comparaison terminée, éléments de différence",
    importParamStart: "Commencer l'importation des paramètres",
    paramReady: "Paramètres prêts à envoyer",
    importSuccess: "Importation réussie",
    exportAllParamStart: "Commencer l'exportation de tous les paramètres",
    exportComplete: "Exportation terminée",
    exportParamStart: "Commencer l'exportation des paramètres de groupe",
    getGroupItemsStart: "Obtenir les éléments de paramètres de groupe",
    getParamValueFailed: "Échec de l'obtention de la valeur du paramètre",
    getGroupItemsComplete: "Obtention terminée, éléments de paramètres",
    getAllGroupItemsStart: "Obtenir tous les éléments de paramètres de groupe",
    groupParamCount: "Groupe : {group}, éléments de paramètres : {count}",
    getCurrentRunAreaStart: "Obtenir la zone d'exécution actuelle",
    getCurrentRunAreaSuccess: "Obtention réussie",
    getCurrentRunAreaFailed: "Obtention échouée",
    selectRunAreaStart: "Sélectionner la zone de paramétrage",
    runAreaEmpty: "La zone de paramétrage ne peut pas être vide",
    selectRunAreaSuccess: "Sélection réussie",
    selectRunAreaFailed: "Sélection échouée"
  },
  debugInfoMenuService: {
    initialized: "Initialisation terminée",
    getDebugInfoEntry: "getDebugInfo paramètres d'entrée",
    getTreeMenuError: "getTreeMenu exception",
    getTreeMenuComplete: "Traitement terminé, nombre de menus"
  },
  deviceInfoController: {
    initialized: "Contrôleur initialisé",
    getDeviceInfoStart: "Commencer à obtenir les informations du dispositif, paramètres de demande",
    getDeviceInfoCheckConnection: "Vérifier l'état de connexion du dispositif",
    getDeviceInfoNotConnected: "Dispositif non connecté, impossible d'obtenir les informations du dispositif",
    getDeviceInfoConnected: "Dispositif connecté, appeler la couche de service pour obtenir les informations du dispositif",
    getDeviceInfoSuccess: "Obtenir les informations du dispositif réussi, nombre de résultats",
    getDeviceInfoException: "Exception lors de l'obtention des informations du dispositif",
    exportDeviceInfoStart: "Commencer à exporter les informations du dispositif, paramètres de demande",
    exportDeviceInfoCheckConnection: "Vérifier l'état de connexion du dispositif",
    exportDeviceInfoNotConnected: "Dispositif non connecté, impossible d'exporter les informations du dispositif",
    exportDeviceInfoValidateParams: "Valider les paramètres d'exportation, nombre de données",
    exportDeviceInfoEmptyData: "Les données d'exportation sont vides",
    exportDeviceInfoEmptyPath: "Le chemin du fichier d'exportation est vide",
    exportDeviceInfoFileExtension: "Extension de fichier",
    exportDeviceInfoUnsupportedFormat: "Format de fichier non pris en charge",
    exportDeviceInfoDirPath: "Chemin du répertoire d'exportation",
    exportDeviceInfoCreateDir: "Créer le répertoire d'exportation",
    exportDeviceInfoCreateDirFailed: "Échec de la création du répertoire d'exportation",
    exportDeviceInfoCallService: "Appeler la couche de service pour exporter les informations du dispositif",
    exportDeviceInfoSuccess: "Exportation des informations du dispositif réussie, chemin d'exportation",
    exportDeviceInfoException: "Exception lors de l'exportation des informations du dispositif"
  },
  variableController: {
    getVariableEntry: "getVariable paramètres d'entrée",
    getVariableReturn: "Journal de retour de la méthode obtenir variable",
    getVariableException: "Journal d'exception de la méthode obtenir variable",
    addVariableEntry: "addVariable paramètres d'entrée",
    addVariableReturn: "Journal de retour de la méthode ajouter variable",
    addVariableException: "Journal d'exception de la méthode ajouter variable",
    modifyVariableEntry: "modifyVariable paramètres d'entrée",
    modifyVariableReturn: "Journal de retour de la méthode modifier variable",
    modifyVariableException: "Journal d'exception de la méthode modifier variable",
    deleteVariableEntry: "deleteVariable paramètres d'entrée",
    deleteVariableReturn: "Journal de retour de la méthode supprimer variable",
    deleteVariableException: "Journal d'exception de la méthode supprimer variable",
    exportVariableEntry: "exportVariable paramètres d'entrée",
    exportVariableEmptyPath: "Le chemin d'exportation ne peut pas être vide",
    exportVariableReturn: "Journal de retour de la méthode exporter variable",
    exportVariableException: "Journal d'exception de la méthode exporter variable",
    importVariableEntry: "importVariable paramètres d'entrée",
    importVariableEmptyPath: "Le chemin d'importation ne peut pas être vide",
    importVariableReturn: "Journal de retour de la méthode importer variable",
    importVariableException: "Journal d'exception de la méthode importer variable"
  },
  paramController: {
    initialized: "Contrôleur initialisé",
    getParamStart: "Commencer à obtenir les paramètres du dispositif, paramètres de demande",
    getParamNotConnected: "Dispositif non connecté, impossible d'obtenir les paramètres du dispositif",
    getParamConnected: "Dispositif connecté, appeler la couche de service pour obtenir les paramètres du dispositif",
    getParamSuccess: "Obtenir les paramètres du dispositif réussi, nombre de résultats",
    getParamException: "Exception lors de l'obtention des paramètres du dispositif",
    getAllParamStart: "Commencer à obtenir tous les paramètres du dispositif, paramètres de demande",
    getAllParamNotConnected: "Dispositif non connecté, impossible d'obtenir tous les paramètres du dispositif",
    getAllParamConnected: "Dispositif connecté, appeler la couche de service pour obtenir tous les paramètres du dispositif",
    getAllParamSuccess: "Obtenir tous les paramètres du dispositif réussi, nombre de résultats",
    getAllParamException: "Exception lors de l'obtention de tous les paramètres du dispositif",
    confirmParamStart: "Commencer à modifier les paramètres du dispositif, paramètres de demande",
    confirmParamNotConnected: "Dispositif non connecté, impossible de modifier les paramètres du dispositif",
    confirmParamConnected: "Dispositif connecté, appeler la couche de service pour modifier les paramètres du dispositif",
    confirmParamSuccess: "Modifier les paramètres du dispositif réussi, résultat",
    confirmParamException: "Exception lors de la modification des paramètres du dispositif",
    getDiffParamStart: "Commencer à obtenir les différences de paramètres, paramètres de demande",
    getDiffParamNotConnected: "Dispositif non connecté, impossible d'obtenir les différences de paramètres",
    getDiffParamPath: "Chemin d'importation",
    getDiffParamEmptyPath: "Le chemin d'importation est vide",
    getDiffParamConnected: "Dispositif connecté, appeler la couche de service pour obtenir les différences de paramètres",
    getDiffParamSuccess: "Obtenir les différences de paramètres réussi, résultat",
    getDiffParamException: "Exception lors de l'obtention des différences de paramètres",
    getAllDiffParamStart: "Commencer à obtenir toutes les différences de paramètres, paramètres de demande",
    getAllDiffParamNotConnected: "Dispositif non connecté, impossible d'obtenir toutes les différences de paramètres",
    getAllDiffParamPath: "Chemin d'importation",
    getAllDiffParamEmptyPath: "Le chemin d'importation est vide",
    getAllDiffParamConnected: "Dispositif connecté, appeler la couche de service pour obtenir toutes les différences de paramètres",
    getAllDiffParamSuccess: "Obtenir toutes les différences de paramètres réussi, résultat",
    getAllDiffParamException: "Exception lors de l'obtention de toutes les différences de paramètres",
    importParamStart: "Commencer à importer les paramètres du dispositif, paramètres de demande",
    importParamNotConnected: "Dispositif non connecté, impossible d'importer les paramètres du dispositif",
    importParamConnected: "Dispositif connecté, appeler la couche de service pour importer les paramètres du dispositif",
    importParamSuccess: "Importer les paramètres du dispositif réussi, résultat",
    importParamException: "Exception lors de l'importation des paramètres du dispositif",
    exportParamStart: "Commencer à exporter les paramètres du dispositif, paramètres de demande",
    exportParamNotConnected: "Dispositif non connecté, impossible d'exporter les paramètres du dispositif",
    exportParamPath: "Chemin d'exportation",
    exportParamEmptyPath: "Le chemin d'exportation est vide",
    exportParamConnected: "Dispositif connecté, appeler la couche de service pour exporter les paramètres du dispositif",
    exportParamSuccess: "Exporter les paramètres du dispositif réussi, résultat",
    exportParamException: "Exception lors de l'exportation des paramètres du dispositif",
    exportAllParamStart: "Commencer à exporter tous les paramètres du dispositif, paramètres de demande",
    exportAllParamNotConnected: "Dispositif non connecté, impossible d'exporter tous les paramètres du dispositif",
    exportAllParamPath: "Chemin d'exportation",
    exportAllParamEmptyPath: "Le chemin d'exportation est vide",
    exportAllParamConnected: "Dispositif connecté, appeler la couche de service pour exporter tous les paramètres du dispositif",
    exportAllParamSuccess: "Exporter tous les paramètres du dispositif réussi, résultat",
    exportAllParamException: "Exception lors de l'exportation de tous les paramètres du dispositif",
    getCurrentRunAreaStart: "Commencer à obtenir la zone d'exécution actuelle, paramètres de demande",
    getCurrentRunAreaNotConnected: "Dispositif non connecté, impossible d'obtenir la zone d'exécution actuelle",
    getCurrentRunAreaConnected: "Dispositif connecté, appeler la couche de service pour obtenir la zone d'exécution actuelle",
    getCurrentRunAreaSuccess: "Obtenir la zone d'exécution actuelle réussi, résultat",
    getCurrentRunAreaException: "Exception lors de l'obtention de la zone d'exécution actuelle",
    selectRunAreaStart: "Commencer à sélectionner la zone de paramètres, paramètres de demande",
    selectRunAreaNotConnected: "Dispositif non connecté, impossible de sélectionner la zone de paramètres",
    selectRunAreaConnected: "Dispositif connecté, appeler la couche de service pour sélectionner la zone de paramètres",
    selectRunAreaSuccess: "Sélectionner la zone de paramètres réussi, résultat",
    selectRunAreaException: "Exception lors de la sélection de la zone de paramètres"
  },
  remoteControlController: {
    ykSelectEntry: "Journal d'entrée de la méthode de sélection de contrôle à distance",
    ykSelectReturn: "Journal de retour de la méthode de sélection de contrôle à distance",
    ykSelectException: "Journal d'exception de la méthode de sélection de contrôle à distance"
  },
  baseController: {
    getDeviceInfoStart: "Commencer à obtenir les informations du dispositif, ID du dispositif",
    getDeviceInfoSuccess: "Obtenir les informations du dispositif réussi, ID du dispositif",
    getDeviceInfoNotFound: "Informations du dispositif non trouvées, ID du dispositif",
    getDeviceInfoFailed: "Échec de l'obtention des informations du dispositif, ID du dispositif"
  }
};
