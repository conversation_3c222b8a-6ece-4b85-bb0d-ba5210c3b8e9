/**
 * Mensajes de registro - Español
 */
export default {
  reportController: {
    getCommonReportListEntry: "getCommonReportList parámetros de entrada",
    getCommonReportListReturn: "getCommonReportList retorno",
    getCommonReportListError: "getCommonReportList excepción",
    cancelUploadStart: "Cancelar carga de archivo de onda iniciado",
    cancelUploadError: "Cancelar carga de archivo de onda excepción",
    openWaveFileStart: "Abrir archivo de onda iniciado",
    openWaveFileError: "Abrir archivo de onda excepción",
    getGroupReportStart: "Obtener informe de grupo iniciado",
    getGroupReportError: "Obtener informe de grupo excepción",
    getOperateReportStart: "Obtener informe de operación iniciado",
    getOperateReportError: "Obtener informe de operación excepción",
    getAuditReportStart: "Obtener informe de auditoría iniciado",
    getAuditReportError: "Obtener informe de auditoría excepción",
    exportCommonReportStart: "Exportar informe común iniciado",
    exportCommonReportError: "Exportar informe común excepción",
    clearReportStart: "Limpiar informe iniciado",
    clearReportError: "Limpiar informe excepción",
    refreshReportStart: "Actualizar informe común iniciado",
    refreshReportError: "Actualizar informe común excepción",
    refreshGroupReportStart: "Actualizar informe de grupo iniciado",
    refreshGroupReportError: "Actualizar informe de grupo excepción",
    refreshOperateReportStart: "Actualizar informe de operación iniciado",
    refreshOperateReportError: "Actualizar informe de operación excepción",
    refreshTripReportStart: "Actualizar informe de disparo iniciado",
    refreshTripReportError: "Actualizar informe de disparo excepción",
    uploadWaveStart: "Carga de archivo de onda iniciado",
    uploadWaveError: "Carga de archivo de onda excepción"
  },
  configureService: {
    getConfigureListError: "Error al obtener lista de configuración",
    loadConfigureError: "Error al cargar configuración"
  },
  // Registros del controlador
  configureController: {
    initialized: "Controlador inicializado",
    getConfigureListStart: "Iniciar obtención de lista de configuración",
    getConfigureListError: "Error al obtener lista de configuración",
    addConfigureStart: "Iniciar agregar configuración",
    addConfigureError: "Error al agregar configuración",
    renameConfigureStart: "Iniciar renombrar configuración",
    renameConfigureError: "Error al renombrar configuración",
    removeConfigureStart: "Iniciar eliminar configuración",
    removeConfigureError: "Error al eliminar configuración",
    saveConfigureStart: "Iniciar guardar configuración",
    saveConfigureError: "Error al guardar configuración",
    loadConfigureStart: "Iniciar cargar configuración",
    loadConfigureError: "Error al cargar configuración",
    openConfigureDirStart: "Iniciar abrir directorio de configuración",
    openConfigureDirError: "Error al abrir directorio de configuración"
  },
  deviceConnectController: {
    initialized: "Controlador inicializado",
    connectDeviceStart: "Iniciar conexión de dispositivo, parámetros de conexión",
    connectDeviceCallService: "Llamar capa de servicio para conectar dispositivo",
    connectDeviceServiceResult: "Resultado de retorno de capa de servicio",
    connectDeviceSuccess: "Conexión de dispositivo exitosa",
    connectDeviceGetError: "Obtener información de error, salida de registro",
    connectDeviceFailed: "Conexión de dispositivo falló, información de error",
    connectDeviceException: "Capturar excepción, salida de registro",
    connectDeviceExceptionDetail: "Excepción de conexión de dispositivo",
    disconnectDeviceStart: "Iniciar desconexión de dispositivo, ID de dispositivo",
    disconnectDeviceCheckStatus: "Verificar estado de conexión de dispositivo",
    disconnectDeviceAlready: "Dispositivo ya desconectado, ID de dispositivo",
    disconnectDeviceCallService: "Llamar capa de servicio para desconectar dispositivo",
    disconnectDeviceResult: "Resultado de desconexión",
    disconnectDeviceSuccess: "Desconexión de dispositivo exitosa, ID de dispositivo",
    disconnectDeviceException: "Excepción de desconexión de dispositivo, ID de dispositivo",
    disconnectDeviceFailed: "Desconexión de dispositivo falló, ID de dispositivo"
  },
  deviceOperateController: {
    initialized: "Controlador inicializado",
    addDeviceStart: "Iniciar agregar configuración de dispositivo, parámetros de solicitud",
    addDeviceCallService: "Llamar capa de servicio para agregar configuración de dispositivo",
    addDeviceSuccess: "Agregar configuración de dispositivo exitoso, resultado",
    addDeviceException: "Excepción al agregar configuración de dispositivo",
    updateDeviceStart: "Iniciar actualizar configuración de dispositivo, parámetros de solicitud",
    updateDeviceCallService: "Llamar capa de servicio para actualizar configuración de dispositivo",
    updateDeviceSuccess: "Actualizar configuración de dispositivo exitoso, resultado",
    updateDeviceException: "Excepción al actualizar configuración de dispositivo",
    removeDeviceStart: "Iniciar eliminar configuración de dispositivo, parámetros de solicitud",
    removeDeviceCallService: "Llamar capa de servicio para eliminar configuración de dispositivo, ID de dispositivo",
    removeDeviceResult: "Resultado de eliminar configuración de dispositivo",
    removeDeviceSuccess: "Eliminar configuración de dispositivo exitoso, ID de dispositivo",
    removeDeviceFailed: "Eliminar configuración de dispositivo falló, ID de dispositivo",
    removeDeviceException: "Excepción al eliminar configuración de dispositivo, ID de dispositivo",
    getDeviceListStart: "Iniciar obtener lista de configuración de dispositivo",
    getDeviceListCallService: "Llamar capa de servicio para obtener lista de configuración de dispositivo",
    getDeviceListSuccess: "Obtener lista de configuración de dispositivo exitoso, cantidad de dispositivos",
    getDeviceListException: "Excepción al obtener lista de configuración de dispositivo"
  },
  paramService: {
    getDiffParamComplete: "Comparación completada, grupos de diferencia",
    getAllDiffParamError: "getAllDiffParam error",
    getParamInfoEntry: "getParamInfo parámetros de entrada",
    getParamInfoReturn: "getParamInfo retorno",
    getParamInfoError: "getParamInfo excepción",
    startGetParamInfo: "Comenzar a obtener configuraciones de parámetros, paginación",
    getAllParamInfoStart: "Comenzar a obtener todas las configuraciones de parámetros",
    getAllParamInfoSuccess: "Obtuvo con éxito todas las configuraciones de parámetros, total",
    modifyParamStart: "Comenzar a modificar configuraciones de parámetros",
    validateParam: "Validar elemento de parámetro",
    validateFailed: "Validación de parámetro fallida",
    validatePassed: "Validación de parámetro pasada, listo para enviar",
    setTimeout: "Establecer tiempo de espera",
    sendResponse: "Enviar respuesta",
    modifySuccess: "Modificación exitosa",
    sendFailed: "Envío fallido",
    businessError: "Error de negocio",
    getAllDiffParamStart: "Comenzar comparación de parámetros por lotes",
    excelParseFailed: "Análisis de Excel fallido",
    csvParseFailed: "Análisis de CSV fallido",
    xmlParseFailed: "Análisis de XML fallido",
    fileParseComplete: "Análisis de archivo completado",
    getDiffParamStart: "Comenzar comparación de parámetros de grupo único",
    diffComplete: "Comparación completada, elementos de diferencia",
    importParamStart: "Comenzar importación de configuraciones de parámetros",
    paramReady: "Parámetros listos para enviar",
    importSuccess: "Importación exitosa",
    exportAllParamStart: "Comenzar exportación de todas las configuraciones de parámetros",
    exportComplete: "Exportación completada",
    exportParamStart: "Comenzar exportación de parámetros de grupo",
    getGroupItemsStart: "Obtener elementos de parámetros de grupo",
    getParamValueFailed: "Error al obtener valor de parámetro",
    getGroupItemsComplete: "Obtención completada, elementos de parámetros",
    getAllGroupItemsStart: "Obtener todos los elementos de parámetros de grupo",
    groupParamCount: "Grupo: {group}, elementos de parámetros: {count}",
    getCurrentRunAreaStart: "Obtener área de ejecución actual",
    getCurrentRunAreaSuccess: "Obtención exitosa",
    getCurrentRunAreaFailed: "Obtención fallida",
    selectRunAreaStart: "Seleccionar área de configuración",
    runAreaEmpty: "El área de configuración no puede estar vacía",
    selectRunAreaSuccess: "Selección exitosa",
    selectRunAreaFailed: "Selección fallida"
  },
  debugInfoMenuService: {
    initialized: "Inicialización completada",
    getDebugInfoEntry: "getDebugInfo parámetros de entrada",
    getTreeMenuError: "getTreeMenu excepción",
    getTreeMenuComplete: "Procesamiento completado, cantidad de menús"
  },
  deviceInfoController: {
    initialized: "Controlador inicializado",
    getDeviceInfoStart: "Iniciar obtener información de dispositivo, parámetros de solicitud",
    getDeviceInfoCheckConnection: "Verificar estado de conexión de dispositivo",
    getDeviceInfoNotConnected: "Dispositivo no conectado, no se puede obtener información de dispositivo",
    getDeviceInfoConnected: "Dispositivo conectado, llamar capa de servicio para obtener información de dispositivo",
    getDeviceInfoSuccess: "Obtener información de dispositivo exitoso, cantidad de resultados",
    getDeviceInfoException: "Excepción al obtener información de dispositivo",
    exportDeviceInfoStart: "Iniciar exportar información de dispositivo, parámetros de solicitud",
    exportDeviceInfoCheckConnection: "Verificar estado de conexión de dispositivo",
    exportDeviceInfoNotConnected: "Dispositivo no conectado, no se puede exportar información de dispositivo",
    exportDeviceInfoValidateParams: "Validar parámetros de exportación, cantidad de datos",
    exportDeviceInfoEmptyData: "Datos de exportación están vacíos",
    exportDeviceInfoEmptyPath: "Ruta de archivo de exportación está vacía",
    exportDeviceInfoFileExtension: "Extensión de archivo",
    exportDeviceInfoUnsupportedFormat: "Formato de archivo no soportado",
    exportDeviceInfoDirPath: "Ruta de directorio de exportación",
    exportDeviceInfoCreateDir: "Crear directorio de exportación",
    exportDeviceInfoCreateDirFailed: "Crear directorio de exportación falló",
    exportDeviceInfoCallService: "Llamar capa de servicio para exportar información de dispositivo",
    exportDeviceInfoSuccess: "Exportar información de dispositivo exitoso, ruta de exportación",
    exportDeviceInfoException: "Excepción al exportar información de dispositivo"
  },
  variableController: {
    getVariableEntry: "getVariable parámetros de entrada",
    getVariableReturn: "Registro de retorno del método obtener variable",
    getVariableException: "Registro de excepción del método obtener variable",
    addVariableEntry: "addVariable parámetros de entrada",
    addVariableReturn: "Registro de retorno del método agregar variable",
    addVariableException: "Registro de excepción del método agregar variable",
    modifyVariableEntry: "modifyVariable parámetros de entrada",
    modifyVariableReturn: "Registro de retorno del método modificar variable",
    modifyVariableException: "Registro de excepción del método modificar variable",
    deleteVariableEntry: "deleteVariable parámetros de entrada",
    deleteVariableReturn: "Registro de retorno del método eliminar variable",
    deleteVariableException: "Registro de excepción del método eliminar variable",
    exportVariableEntry: "exportVariable parámetros de entrada",
    exportVariableEmptyPath: "La ruta de exportación no puede estar vacía",
    exportVariableReturn: "Registro de retorno del método exportar variable",
    exportVariableException: "Registro de excepción del método exportar variable",
    importVariableEntry: "importVariable parámetros de entrada",
    importVariableEmptyPath: "La ruta de importación no puede estar vacía",
    importVariableReturn: "Registro de retorno del método importar variable",
    importVariableException: "Registro de excepción del método importar variable"
  },
  paramController: {
    initialized: "Controlador inicializado",
    getParamStart: "Iniciar obtener parámetros de dispositivo, parámetros de solicitud",
    getParamNotConnected: "Dispositivo no conectado, no se pueden obtener parámetros de dispositivo",
    getParamConnected: "Dispositivo conectado, llamar capa de servicio para obtener parámetros de dispositivo",
    getParamSuccess: "Obtener parámetros de dispositivo exitoso, cantidad de resultados",
    getParamException: "Excepción al obtener parámetros de dispositivo",
    getAllParamStart: "Iniciar obtener todos los parámetros de dispositivo, parámetros de solicitud",
    getAllParamNotConnected: "Dispositivo no conectado, no se pueden obtener todos los parámetros de dispositivo",
    getAllParamConnected: "Dispositivo conectado, llamar capa de servicio para obtener todos los parámetros de dispositivo",
    getAllParamSuccess: "Obtener todos los parámetros de dispositivo exitoso, cantidad de resultados",
    getAllParamException: "Excepción al obtener todos los parámetros de dispositivo",
    confirmParamStart: "Iniciar modificar parámetros de dispositivo, parámetros de solicitud",
    confirmParamNotConnected: "Dispositivo no conectado, no se pueden modificar parámetros de dispositivo",
    confirmParamConnected: "Dispositivo conectado, llamar capa de servicio para modificar parámetros de dispositivo",
    confirmParamSuccess: "Modificar parámetros de dispositivo exitoso, resultado",
    confirmParamException: "Excepción al modificar parámetros de dispositivo",
    getDiffParamStart: "Iniciar obtener diferencias de parámetros, parámetros de solicitud",
    getDiffParamNotConnected: "Dispositivo no conectado, no se pueden obtener diferencias de parámetros",
    getDiffParamPath: "Ruta de importación",
    getDiffParamEmptyPath: "La ruta de importación está vacía",
    getDiffParamConnected: "Dispositivo conectado, llamar capa de servicio para obtener diferencias de parámetros",
    getDiffParamSuccess: "Obtener diferencias de parámetros exitoso, resultado",
    getDiffParamException: "Excepción al obtener diferencias de parámetros",
    getAllDiffParamStart: "Iniciar obtener todas las diferencias de parámetros, parámetros de solicitud",
    getAllDiffParamNotConnected: "Dispositivo no conectado, no se pueden obtener todas las diferencias de parámetros",
    getAllDiffParamPath: "Ruta de importación",
    getAllDiffParamEmptyPath: "La ruta de importación está vacía",
    getAllDiffParamConnected: "Dispositivo conectado, llamar capa de servicio para obtener todas las diferencias de parámetros",
    getAllDiffParamSuccess: "Obtener todas las diferencias de parámetros exitoso, resultado",
    getAllDiffParamException: "Excepción al obtener todas las diferencias de parámetros",
    importParamStart: "Iniciar importar parámetros de dispositivo, parámetros de solicitud",
    importParamNotConnected: "Dispositivo no conectado, no se pueden importar parámetros de dispositivo",
    importParamConnected: "Dispositivo conectado, llamar capa de servicio para importar parámetros de dispositivo",
    importParamSuccess: "Importar parámetros de dispositivo exitoso, resultado",
    importParamException: "Excepción al importar parámetros de dispositivo",
    exportParamStart: "Iniciar exportar parámetros de dispositivo, parámetros de solicitud",
    exportParamNotConnected: "Dispositivo no conectado, no se pueden exportar parámetros de dispositivo",
    exportParamPath: "Ruta de exportación",
    exportParamEmptyPath: "La ruta de exportación está vacía",
    exportParamConnected: "Dispositivo conectado, llamar capa de servicio para exportar parámetros de dispositivo",
    exportParamSuccess: "Exportar parámetros de dispositivo exitoso, resultado",
    exportParamException: "Excepción al exportar parámetros de dispositivo",
    exportAllParamStart: "Iniciar exportar todos los parámetros de dispositivo, parámetros de solicitud",
    exportAllParamNotConnected: "Dispositivo no conectado, no se pueden exportar todos los parámetros de dispositivo",
    exportAllParamPath: "Ruta de exportación",
    exportAllParamEmptyPath: "La ruta de exportación está vacía",
    exportAllParamConnected: "Dispositivo conectado, llamar capa de servicio para exportar todos los parámetros de dispositivo",
    exportAllParamSuccess: "Exportar todos los parámetros de dispositivo exitoso, resultado",
    exportAllParamException: "Excepción al exportar todos los parámetros de dispositivo",
    getCurrentRunAreaStart: "Iniciar obtener área de ejecución actual, parámetros de solicitud",
    getCurrentRunAreaNotConnected: "Dispositivo no conectado, no se puede obtener área de ejecución actual",
    getCurrentRunAreaConnected: "Dispositivo conectado, llamar capa de servicio para obtener área de ejecución actual",
    getCurrentRunAreaSuccess: "Obtener área de ejecución actual exitoso, resultado",
    getCurrentRunAreaException: "Excepción al obtener área de ejecución actual",
    selectRunAreaStart: "Iniciar seleccionar área de parámetros, parámetros de solicitud",
    selectRunAreaNotConnected: "Dispositivo no conectado, no se puede seleccionar área de parámetros",
    selectRunAreaConnected: "Dispositivo conectado, llamar capa de servicio para seleccionar área de parámetros",
    selectRunAreaSuccess: "Seleccionar área de parámetros exitoso, resultado",
    selectRunAreaException: "Excepción al seleccionar área de parámetros"
  },
  remoteControlController: {
    ykSelectEntry: "Registro de entrada del método de selección de control remoto",
    ykSelectReturn: "Registro de retorno del método de selección de control remoto",
    ykSelectException: "Registro de excepción del método de selección de control remoto"
  },
  baseController: {
    getDeviceInfoStart: "Iniciar obtener información de dispositivo, ID de dispositivo",
    getDeviceInfoSuccess: "Obtener información de dispositivo exitoso, ID de dispositivo",
    getDeviceInfoNotFound: "Información de dispositivo no encontrada, ID de dispositivo",
    getDeviceInfoFailed: "Error al obtener información de dispositivo, ID de dispositivo"
  }
};
